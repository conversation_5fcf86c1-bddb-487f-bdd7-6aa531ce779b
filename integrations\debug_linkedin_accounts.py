#!/usr/bin/env python3
"""
Debug script to check LinkedIn accounts in Unipile
"""

from linkedin_integration.linkedin_api import LinkedInMessaging
import json

def debug_accounts():
    print("🔍 Debugging LinkedIn Accounts in Unipile")
    print("=" * 50)
    
    try:
        linkedin = LinkedInMessaging()
        print("✅ LinkedIn instance created")
        
        # Get raw accounts
        accounts = linkedin.unipile_client.get_accounts()
        print(f"\n📊 Raw accounts response:")
        print(json.dumps(accounts, indent=2))
        
        if "error" in accounts:
            print(f"❌ Error getting accounts: {accounts['error']}")
            return
        
        # Handle both response formats
        all_accounts = accounts.get("accounts", accounts.get("items", []))
        print(f"\n📋 Total accounts found: {len(all_accounts)}")

        if all_accounts:
            print("\n🔍 Account details:")
            for i, account in enumerate(all_accounts, 1):
                print(f"\nAccount {i}:")
                print(f"  ID: {account.get('id')}")
                print(f"  Name: {account.get('name')}")
                print(f"  Type: {account.get('type')}")
                print(f"  Created: {account.get('created_at')}")

                # Check if it's LinkedIn
                if account.get('type') == 'LINKEDIN':
                    print("  ✅ This is a LinkedIn account!")
                else:
                    print(f"  ❌ Not LinkedIn (type: {account.get('type')})")

        # Test filtering
        linkedin_accounts = [acc for acc in all_accounts if acc.get("type") == "LINKEDIN"]
        print(f"\n🎯 LinkedIn accounts after filtering: {len(linkedin_accounts)}")
        
        if linkedin_accounts:
            print("✅ LinkedIn accounts found!")
            for account in linkedin_accounts:
                print(f"  - {account.get('name')} ({account.get('id')})")
        else:
            print("❌ No LinkedIn accounts found after filtering")
            
            # Check what types we do have
            types_found = [acc.get('type') for acc in all_accounts]
            print(f"Types found: {types_found}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_accounts()
