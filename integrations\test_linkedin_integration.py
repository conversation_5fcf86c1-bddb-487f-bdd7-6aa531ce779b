#!/usr/bin/env python3
"""
Test script to verify LinkedIn integration with Unipile API
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_linkedin_api_import():
    """Test LinkedIn API import and initialization"""
    print("📋 Testing LinkedIn API Import")
    print("=" * 40)
    
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        print("✅ Successfully imported LinkedInMessaging")
        
        # Create instance
        linkedin = LinkedInMessaging()
        print("✅ Successfully created LinkedInMessaging instance")
        
        # Check if Unipile client is initialized
        if hasattr(linkedin, 'unipile_client'):
            print("✅ Unipile client initialized")
        else:
            print("❌ Unipile client not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing LinkedIn API: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unipile_connection():
    """Test Unipile API connection for LinkedIn"""
    print("\n🔗 Testing Unipile Connection for LinkedIn")
    print("=" * 40)
    
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        
        linkedin = LinkedInMessaging()
        
        # Test Unipile connection
        accounts = linkedin.unipile_client.get_accounts()
        
        if "error" in accounts:
            print(f"❌ Unipile connection failed: {accounts['error']}")
            return False
        else:
            print("✅ Unipile API connection successful!")
            
            # Show account information
            all_accounts = accounts.get("accounts", [])
            linkedin_accounts = [acc for acc in all_accounts if acc.get("provider") == "linkedin"]
            
            print(f"📊 Total accounts: {len(all_accounts)}")
            print(f"💼 LinkedIn accounts: {len(linkedin_accounts)}")
            
            if linkedin_accounts:
                print("\n📋 LinkedIn Accounts Found:")
                for i, account in enumerate(linkedin_accounts, 1):
                    print(f"   {i}. ID: {account.get('id')}")
                    print(f"      Name: {account.get('name', 'N/A')}")
                    print(f"      Email: {account.get('email', 'N/A')}")
                    print(f"      Company: {account.get('company', 'N/A')}")
                    print(f"      Status: {account.get('status', 'Unknown')}")
                    print()
            else:
                print("\n⚠️  No LinkedIn accounts found in Unipile dashboard")
                print("💡 To add a LinkedIn account:")
                print("   1. Visit https://dashboard.unipile.com")
                print("   2. Click 'Add Account' → 'LinkedIn'")
                print("   3. Follow the authentication process")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing Unipile connection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_linkedin_methods():
    """Test LinkedIn messaging methods"""
    print("\n💼 Testing LinkedIn Messaging Methods")
    print("=" * 40)
    
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        
        linkedin = LinkedInMessaging()
        
        # Test authenticate_account method
        print("🔐 Testing authenticate_account...")
        auth_result = linkedin.authenticate_account()
        print(f"   Result: {auth_result.get('success', False)}")
        if not auth_result.get('success'):
            print(f"   Message: {auth_result.get('message', 'No message')}")
        
        # Test connection status
        print("\n📊 Testing get_connection_status...")
        status = linkedin.get_connection_status()
        print(f"   Unipile available: {status.get('unipile', {}).get('available', False)}")
        print(f"   LinkedIn API available: {status.get('linkedin_api', {}).get('available', False)}")
        
        # Test methods exist
        required_methods = [
            'send_inmail',
            'send_connection_message',
            'send_company_page_message',
            'get_profile',
            'get_connections'
        ]
        
        print("\n🔍 Checking required methods...")
        for method in required_methods:
            if hasattr(linkedin, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} - MISSING")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing LinkedIn methods: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """Test that API endpoints are available"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 40)
    
    try:
        # Test importing the API endpoints
        from api.api_endpoints import app
        print("✅ Successfully imported FastAPI app")
        
        # Check if LinkedIn endpoints are defined
        routes = [route.path for route in app.routes]
        
        expected_endpoints = [
            "/api/linkedin/unipile/connect",
            "/api/linkedin/unipile/status",
            "/api/linkedin/config",
            "/api/linkedin/profile",
            "/api/linkedin/connections",
            "/api/linkedin/send-inmail",
            "/api/linkedin/send-connection-message"
        ]
        
        print("🔍 Checking API endpoints...")
        for endpoint in expected_endpoints:
            if endpoint in routes:
                print(f"   ✅ {endpoint}")
            else:
                print(f"   ❌ {endpoint} - MISSING")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing API endpoints: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing LinkedIn Integration")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("LinkedIn API Import", test_linkedin_api_import),
        ("Unipile Connection", test_unipile_connection),
        ("LinkedIn Methods", test_linkedin_methods),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! LinkedIn integration is working.")
        print("\n💡 Next steps:")
        print("   1. Add LinkedIn accounts to your Unipile dashboard")
        print("   2. Visit: https://dashboard.unipile.com")
        print("   3. Click 'Add Account' → 'LinkedIn'")
        print("   4. Complete the authentication process")
        print("   5. Your LinkedIn accounts will appear in the dashboard")
        print("   6. Use the integration for InMail, connection messages, and more")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
    
    print("\n🔗 Unipile Dashboard: https://dashboard.unipile.com")
    print("💼 LinkedIn Developer Portal: https://developer.linkedin.com")
    print("📖 Unipile Documentation: https://docs.unipile.com")
