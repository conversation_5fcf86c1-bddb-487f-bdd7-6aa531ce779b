<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test LinkedIn Connect Endpoint</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #0077b5;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #005885;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test LinkedIn Connect Endpoint</h1>
        <p>This page tests the <code>/api/linkedin/connect-account</code> endpoint to verify it's working correctly.</p>
        
        <div class="form-group">
            <label for="username">LinkedIn Username/Email:</label>
            <input type="text" id="username" placeholder="<EMAIL>" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="password">LinkedIn Password:</label>
            <input type="password" id="password" placeholder="testpass" value="testpass">
        </div>
        
        <button onclick="testEndpoint()">🚀 Test Connect Endpoint</button>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
            <h3>📋 Expected Results:</h3>
            <ul>
                <li><strong>Status 400</strong>: Endpoint working, invalid credentials (expected)</li>
                <li><strong>Status 404</strong>: Endpoint not found (problem)</li>
                <li><strong>Status 500</strong>: Server error (problem)</li>
                <li><strong>Status 200</strong>: Success (would happen with real credentials)</li>
            </ul>
        </div>
    </div>

    <script>
        function testEndpoint() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter both username and password</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">🔄 Testing endpoint...</div>';
            
            fetch('/api/linkedin/connect-account', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => {
                const statusCode = response.status;
                return response.json().then(data => ({ statusCode, data }));
            })
            .then(({ statusCode, data }) => {
                let resultClass = 'info';
                let icon = '📊';
                let message = '';
                
                if (statusCode === 404) {
                    resultClass = 'error';
                    icon = '❌';
                    message = 'ENDPOINT NOT FOUND - This is a problem!';
                } else if (statusCode === 400) {
                    resultClass = 'success';
                    icon = '✅';
                    message = 'ENDPOINT WORKING - Expected error with test credentials';
                } else if (statusCode === 500) {
                    resultClass = 'error';
                    icon = '❌';
                    message = 'SERVER ERROR - Check server logs';
                } else if (statusCode === 200) {
                    resultClass = 'success';
                    icon = '🎉';
                    message = 'SUCCESS - Account connected!';
                } else {
                    resultClass = 'info';
                    icon = '📊';
                    message = `Unexpected status code: ${statusCode}`;
                }
                
                resultDiv.innerHTML = `
                    <div class="${resultClass}">
                        <strong>${icon} ${message}</strong><br>
                        <strong>Status Code:</strong> ${statusCode}<br>
                        <strong>Response:</strong> ${JSON.stringify(data, null, 2)}
                    </div>
                `;
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ CONNECTION ERROR</strong><br>
                        This usually means the server is not running.<br>
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            });
        }
        
        // Auto-test on page load
        window.onload = function() {
            setTimeout(testEndpoint, 1000);
        };
    </script>
</body>
</html>
