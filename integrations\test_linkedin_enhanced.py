#!/usr/bin/env python3
"""
Test script for enhanced LinkedIn integration with hosted authentication
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_linkedin_integration():
    """Test the enhanced LinkedIn integration features"""
    print("🔗 Testing Enhanced LinkedIn Integration")
    print("=" * 50)
    
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        
        # Test basic initialization
        print("\n1. 📋 Testing LinkedIn Integration Initialization")
        linkedin = LinkedInMessaging()
        print("   ✅ LinkedIn integration initialized successfully")
        
        # Test config loading
        print(f"   📁 Config path: {linkedin.config_path}")
        print(f"   🔧 Config loaded: {bool(linkedin.config)}")
        print(f"   🌐 Unipile client available: {bool(linkedin.unipile_client)}")
        
        # Test connection status
        print("\n2. 🔍 Testing Connection Status")
        status = linkedin.get_connection_status()
        print(f"   📊 Status: {status}")
        
        # Test account ID retrieval
        print("\n3. 🆔 Testing Account ID Retrieval")
        account_id = linkedin._get_linkedin_account_id()
        print(f"   🔑 Account ID: {account_id or 'Not available'}")
        
        # Test Unipile client methods
        print("\n4. 🌐 Testing Unipile Client")
        if linkedin.unipile_client:
            print("   ✅ Unipile client is available")
            
            # Test accounts endpoint
            try:
                accounts = linkedin.unipile_client.get_accounts()
                if "error" in accounts:
                    print(f"   ⚠️  Accounts API error: {accounts['error']}")
                else:
                    print(f"   📋 Accounts retrieved: {len(accounts.get('accounts', []))} total")
                    linkedin_accounts = [acc for acc in accounts.get('accounts', []) 
                                       if acc.get('provider') == 'linkedin']
                    print(f"   🔗 LinkedIn accounts: {len(linkedin_accounts)}")
            except Exception as e:
                print(f"   ❌ Accounts API error: {e}")
        else:
            print("   ❌ Unipile client not available")
        
        # Test message sending (dry run)
        print("\n5. 📤 Testing Message Methods (Dry Run)")
        
        # Test connection message
        try:
            result = linkedin.send_connection_message("test_recipient", "Test message")
            print(f"   🤝 Connection message result: {result.get('error', 'Success simulation')}")
        except Exception as e:
            print(f"   ❌ Connection message error: {e}")
        
        # Test InMail
        try:
            result = linkedin.send_inmail("test_recipient", "Test Subject", "Test message")
            print(f"   📧 InMail result: {result.get('error', 'Success simulation')}")
        except Exception as e:
            print(f"   ❌ InMail error: {e}")
        
        print("\n6. 🎯 Testing Enhanced Features")
        
        # Test the helper method
        print(f"   🔧 Helper method available: {hasattr(linkedin, '_get_linkedin_account_id')}")
        print(f"   💾 Account ID caching: {hasattr(linkedin, '_linkedin_account_id')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints_structure():
    """Test that the API endpoints are properly structured"""
    print("\n🌐 Testing API Endpoints Structure")
    print("=" * 40)
    
    try:
        from api.api_endpoints import app
        
        # Get all LinkedIn routes
        linkedin_routes = [route for route in app.routes if hasattr(route, 'path') and 'linkedin' in route.path]
        
        print(f"📋 Found {len(linkedin_routes)} LinkedIn endpoints:")
        for route in linkedin_routes:
            methods = getattr(route, 'methods', ['GET'])
            print(f"   {list(methods)[0]:6} {route.path}")
        
        # Check for new endpoints
        expected_new_endpoints = [
            '/api/linkedin/hosted-auth',
            '/api/linkedin/connect-account',
            '/api/linkedin/auth-callback'
        ]
        
        existing_paths = [route.path for route in linkedin_routes]
        
        print("\n🔍 Checking for new endpoints:")
        for endpoint in expected_new_endpoints:
            if endpoint in existing_paths:
                print(f"   ✅ {endpoint}")
            else:
                print(f"   ❌ {endpoint} - MISSING")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing API endpoints: {e}")
        return False

def test_html_interface():
    """Test that the HTML interface has been enhanced"""
    print("\n📄 Testing HTML Interface Enhancements")
    print("=" * 40)
    
    try:
        html_path = "linkedin_integration/linkedin_auth.html"
        
        if not os.path.exists(html_path):
            print(f"❌ HTML file not found: {html_path}")
            return False
        
        with open(html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for new features
        features_to_check = [
            'createHostedAuth',
            'connectLinkedInDirect',
            'Method 1: Hosted Authentication',
            'Method 2: Direct Credentials',
            'hostedAuthResult',
            'directConnectResult'
        ]
        
        print("🔍 Checking for enhanced features:")
        for feature in features_to_check:
            if feature in content:
                print(f"   ✅ {feature}")
            else:
                print(f"   ❌ {feature} - MISSING")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing HTML interface: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 LinkedIn Integration Enhanced Testing Suite")
    print("=" * 60)
    
    tests = [
        test_linkedin_integration,
        test_api_endpoints_structure,
        test_html_interface
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n📊 Test Summary")
    print("=" * 20)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! LinkedIn integration is enhanced and ready.")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
