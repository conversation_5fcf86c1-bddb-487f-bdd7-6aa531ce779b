<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Chat ID Helper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #0088cc;
            margin-bottom: 30px;
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #0088cc;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .button {
            background: #0088cc;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #006699;
        }
        .test-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            border: 1px solid #dee2e6;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .bot-info {
            background: #cce5ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Telegram Chat ID Helper</h1>
            <p>Get your chat ID to test the Telegram bot</p>
        </div>

        <div class="step">
            <h3>📱 Step 1: Start a conversation with your bot</h3>
            <p>1. Open Telegram on your phone or computer</p>
            <p>2. Search for <strong>@Amiee2_bot</strong></p>
            <p>3. Send any message to the bot (like "hello" or "/start")</p>
        </div>

        <div class="step">
            <h3>🔍 Step 2: Get recent chat IDs</h3>
            <p>Click the button below to fetch recent messages and get your chat ID:</p>
            <button class="button" onclick="getChatIds()">Get Chat IDs</button>
            <div id="chatIdsResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📤 Step 3: Test sending a message</h3>
            <div class="input-group">
                <label for="chatId">Chat ID:</label>
                <input type="text" id="chatId" placeholder="Enter your chat ID (e.g., 123456789)">
            </div>
            <div class="input-group">
                <label for="message">Test Message:</label>
                <textarea id="message" rows="3" placeholder="Enter your test message">Hello! This is a test message from your Telegram bot.</textarea>
            </div>
            <button class="button" onclick="sendTestMessage()">Send Test Message</button>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>🔧 Alternative: Use Bot API directly</h3>
            <p>You can also get your chat ID by visiting this URL in your browser after messaging the bot:</p>
            <div class="code">
https://api.telegram.org/bot7068394341:AAG6Atzujl8jxkvQuJHo4Q3ODp6FOnxlTf4/getUpdates
            </div>
            <p>Look for the "chat" object in the response to find your chat ID.</p>
        </div>
    </div>

    <script>
        async function getChatIds() {
            const resultDiv = document.getElementById('chatIdsResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 Fetching chat IDs...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/api/telegram/updates');
                const data = await response.json();

                if (data.success && data.data.ok) {
                    const updates = data.data.result;
                    
                    if (updates.length === 0) {
                        resultDiv.innerHTML = `
                            <strong>⚠️ No recent messages found!</strong><br>
                            Please send a message to @Amiee2_bot first, then try again.
                        `;
                        resultDiv.className = 'result error';
                        return;
                    }

                    const chatIds = new Set();
                    const chatInfo = {};

                    updates.forEach(update => {
                        if (update.message && update.message.chat) {
                            const chat = update.message.chat;
                            const chatId = chat.id;
                            chatIds.add(chatId);
                            chatInfo[chatId] = {
                                type: chat.type,
                                first_name: chat.first_name,
                                last_name: chat.last_name,
                                username: chat.username,
                                title: chat.title
                            };
                        }
                    });

                    let html = `<strong>✅ Found ${chatIds.size} chat(s):</strong><br><br>`;
                    
                    chatIds.forEach(chatId => {
                        const info = chatInfo[chatId];
                        html += `<div style="margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px;">`;
                        html += `<strong>Chat ID: ${chatId}</strong><br>`;
                        html += `Type: ${info.type}<br>`;
                        
                        if (info.type === 'private') {
                            const name = [info.first_name, info.last_name].filter(Boolean).join(' ');
                            html += `Name: ${name}<br>`;
                            if (info.username) html += `Username: @${info.username}<br>`;
                        } else if (info.type === 'group' || info.type === 'supergroup') {
                            html += `Title: ${info.title}<br>`;
                            if (info.username) html += `Username: @${info.username}<br>`;
                        }
                        
                        html += `<button class="button" onclick="document.getElementById('chatId').value='${chatId}'" style="margin-top: 5px; padding: 5px 10px; font-size: 12px;">Use This Chat ID</button>`;
                        html += `</div>`;
                    });

                    resultDiv.innerHTML = html;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<strong>❌ Error:</strong> ${data.error || 'Failed to get updates'}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function sendTestMessage() {
            const chatId = document.getElementById('chatId').value.trim();
            const message = document.getElementById('message').value.trim();
            const resultDiv = document.getElementById('testResult');

            if (!chatId) {
                alert('Please enter a chat ID');
                return;
            }

            if (!message) {
                alert('Please enter a message');
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 Sending message...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/api/telegram/send-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        chat_id: chatId,
                        message: message
                    })
                });

                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `
                        <strong>✅ Message sent successfully!</strong><br>
                        Message ID: ${data.result.message_id || 'N/A'}<br>
                        Timestamp: ${data.timestamp}
                    `;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<strong>❌ Failed to send message:</strong><br>${data.detail || data.error}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
