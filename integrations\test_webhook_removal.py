#!/usr/bin/env python3
"""
Test script to verify that all webhook functionality has been removed
"""

import sys
import os
import json
import traceback

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_telegram_api_no_webhook():
    """Test that TelegramMessaging class has no webhook methods"""
    print("🔍 Testing TelegramMessaging class for webhook removal...")
    
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        
        telegram = TelegramMessaging()
        
        # Check that webhook methods don't exist
        webhook_methods = ['set_webhook', 'get_webhook_info', 'delete_webhook']
        
        for method in webhook_methods:
            if hasattr(telegram, method):
                print(f"❌ ERROR: Method '{method}' still exists in TelegramMessaging")
                return False
            else:
                print(f"✅ Method '{method}' successfully removed")
        
        # Check that webhook_url property doesn't exist
        if hasattr(telegram, 'webhook_url'):
            print("❌ ERROR: Property 'webhook_url' still exists in TelegramMessaging")
            return False
        else:
            print("✅ Property 'webhook_url' successfully removed")
        
        # Test update_config method signature
        try:
            # This should work (only bot_token parameter)
            result = telegram.update_config(bot_token="test_token")
            print("✅ update_config method works with bot_token only")
        except TypeError as e:
            if "webhook_url" in str(e):
                print("❌ ERROR: update_config still expects webhook_url parameter")
                return False
            else:
                print(f"✅ update_config method signature updated correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing TelegramMessaging: {e}")
        traceback.print_exc()
        return False

def test_legacy_api_no_webhook():
    """Test that legacy TelegramAPI class has no webhook methods"""
    print("\n🔍 Testing legacy TelegramAPI class for webhook removal...")
    
    try:
        from telegram_integration.telegram_api import TelegramAPI
        
        telegram = TelegramAPI()
        
        # Check that webhook methods don't exist
        webhook_methods = ['set_webhook', 'get_webhook_info', 'delete_webhook']
        
        for method in webhook_methods:
            if hasattr(telegram, method):
                print(f"❌ ERROR: Method '{method}' still exists in TelegramAPI")
                return False
            else:
                print(f"✅ Method '{method}' successfully removed")
        
        # Test update_config method signature
        try:
            # This should work (only bot_token parameter)
            telegram.update_config(bot_token="test_token")
            print("✅ update_config method works with bot_token only")
        except TypeError as e:
            if "webhook_url" in str(e):
                print("❌ ERROR: update_config still expects webhook_url parameter")
                return False
            else:
                print(f"✅ update_config method signature updated correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing TelegramAPI: {e}")
        traceback.print_exc()
        return False

def test_config_files():
    """Test that config files don't contain webhook references"""
    print("\n🔍 Testing config files for webhook removal...")
    
    success = True
    
    # Test telegram config.json
    try:
        config_path = os.path.join(os.path.dirname(__file__), "telegram_integration", "config.json")
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        if "webhook_url" in config:
            print("❌ ERROR: 'webhook_url' still exists in telegram config.json")
            success = False
        else:
            print("✅ 'webhook_url' successfully removed from telegram config.json")
            
    except Exception as e:
        print(f"❌ Error testing telegram config.json: {e}")
        success = False
    
    # Test unified messaging config
    try:
        config_path = os.path.join(os.path.dirname(__file__), "unified_messaging_config.json")
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        if "webhook_settings" in config:
            print("❌ ERROR: 'webhook_settings' still exists in unified_messaging_config.json")
            success = False
        else:
            print("✅ 'webhook_settings' successfully removed from unified_messaging_config.json")
            
    except Exception as e:
        print(f"❌ Error testing unified_messaging_config.json: {e}")
        success = False
    
    return success

def test_api_endpoints():
    """Test that API endpoints don't have webhook routes"""
    print("\n🔍 Testing API endpoints for webhook removal...")
    
    try:
        # Read the API endpoints file
        api_file = os.path.join(os.path.dirname(__file__), "api", "api_endpoints.py")
        with open(api_file, 'r') as f:
            content = f.read()
        
        # Check for webhook-related endpoints
        webhook_patterns = [
            "/api/telegram/webhook",
            "/api/telegram/webhook-info",
            "TelegramWebhookRequest",
            "setup_telegram_webhook",
            "get_telegram_webhook_info"
        ]
        
        success = True
        for pattern in webhook_patterns:
            if pattern in content:
                print(f"❌ ERROR: '{pattern}' still exists in API endpoints")
                success = False
            else:
                print(f"✅ '{pattern}' successfully removed from API endpoints")
        
        # Check that TelegramConfigRequest doesn't have webhook_url
        if "webhook_url" in content and "TelegramConfigRequest" in content:
            # More specific check
            lines = content.split('\n')
            in_config_request = False
            for line in lines:
                if "class TelegramConfigRequest" in line:
                    in_config_request = True
                elif in_config_request and "class " in line and "TelegramConfigRequest" not in line:
                    in_config_request = False
                elif in_config_request and "webhook_url" in line:
                    print("❌ ERROR: 'webhook_url' still exists in TelegramConfigRequest")
                    success = False
                    break
            else:
                if in_config_request:
                    print("✅ 'webhook_url' successfully removed from TelegramConfigRequest")
        
        return success
        
    except Exception as e:
        print(f"❌ Error testing API endpoints: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing Webhook Functionality Removal")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("TelegramMessaging Class", test_telegram_api_no_webhook),
        ("Legacy TelegramAPI Class", test_legacy_api_no_webhook),
        ("Config Files", test_config_files),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Webhook functionality has been completely removed.")
        print("\n💡 The Telegram integration now works with polling only:")
        print("   - No webhook setup required")
        print("   - Uses getUpdates for receiving messages")
        print("   - Simplified configuration (bot_token only)")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
