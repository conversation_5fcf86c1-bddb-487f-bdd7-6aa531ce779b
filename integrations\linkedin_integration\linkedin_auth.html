<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Professional Messaging</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #0077b5;
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
            margin: 0;
        }
        
        .section {
            background: #f8f9fa;
            border-left: 4px solid #0077b5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .section h3 {
            color: #0077b5;
            margin-top: 0;
            font-size: 1.3em;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #0077b5;
        }
        
        .button {
            background: #0077b5;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #005885;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .info {
            background: #cce5ff;
            border: 1px solid #99ccff;
            color: #004085;
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .messaging-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .messaging-card h4 {
            color: #0077b5;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .messaging-card p {
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💼 LinkedIn Professional Messaging</h1>
            <p>Connection requests, InMail, and company page messaging</p>
        </div>

        <!-- Connection Setup -->
        <div class="section">
            <h3>🔗 Step 1: Connect Your LinkedIn Account</h3>
            <div class="info">
                <strong>Connect via Unipile to enable professional messaging features</strong>
            </div>

            <div class="form-group">
                <label for="unipileApiKey">Unipile API Key:</label>
                <input type="password" id="unipileApiKey" name="unipileApiKey"
                       value="RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="
                       placeholder="Your Unipile API key">
            </div>

            <button type="button" onclick="connectUnipile()">Connect API Key</button>
            <button type="button" onclick="checkUnipileStatus()">Check Connection Status</button>

            <div id="unipileResult"></div>
            <div id="connectionStatus"></div>

            <!-- LinkedIn Account Connection -->
            <div class="form-group" style="margin-top: 20px;">
                <h4>🔐 Add LinkedIn Account</h4>
                <div class="info">
                    <strong>Connect your LinkedIn account using your credentials:</strong>
                </div>

                <!-- Direct Credentials Connection -->
                <div style="margin: 15px 0; padding: 15px; border: 2px solid #0077b5; border-radius: 8px;">
                    <h5>🔑 LinkedIn Account Connection</h5>
                    <p>Connect using your LinkedIn username and password. Your account will automatically appear in your Unipile dashboard.</p>
                    <div class="form-group">
                        <label for="linkedinUsername">LinkedIn Username/Email:</label>
                        <input type="text" id="linkedinUsername" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="linkedinPassword">LinkedIn Password:</label>
                        <input type="password" id="linkedinPassword" placeholder="Your LinkedIn password">
                    </div>
                    <button type="button" onclick="connectLinkedInDirect()" style="background: #0077b5;">
                        🔐 Connect LinkedIn Account
                    </button>
                    <div id="directConnectResult"></div>

                    <!-- Checkpoint Verification (hidden by default) -->
                    <div id="checkpointSection" style="display: none; margin-top: 15px; padding: 15px; border: 2px solid #ffc107; border-radius: 8px; background: #fff3cd;">
                        <h5>🔐 Verification Required</h5>
                        <p id="checkpointMessage">LinkedIn requires additional verification to complete the connection.</p>
                        <div class="form-group">
                            <label for="verificationCode">Verification Code:</label>
                            <input type="text" id="verificationCode" placeholder="Enter verification code">
                            <small id="checkpointInstructions">Check your email, SMS, or LinkedIn mobile app for the verification code.</small>
                        </div>
                        <button type="button" onclick="solveCheckpoint()" style="background: #ffc107; color: #212529;">
                            ✅ Verify Code
                        </button>
                        <div id="checkpointResult"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Messaging Features -->
        <div class="section">
            <h3>💬 Step 2: Professional Messaging Features</h3>
            
            <!-- Connection Requests -->
            <div class="messaging-card">
                <h4>🤝 Connection Requests with Messages</h4>
                <p>Send personalized connection requests to expand your professional network</p>
                
                <div class="form-group">
                    <label for="connectionPersonId">LinkedIn Person ID:</label>
                    <input type="text" id="connectionPersonId" placeholder="e.g., ACoAAABCDEF">
                </div>
                
                <div class="form-group">
                    <label for="connectionMessage">Connection Message:</label>
                    <textarea id="connectionMessage" rows="3" placeholder="I'd like to connect with you to expand my professional network."></textarea>
                </div>
                
                <button type="button" onclick="sendConnectionRequest()">Send Connection Request</button>
                <div id="connectionResult"></div>
            </div>

            <!-- InMail -->
            <div class="messaging-card">
                <h4>📧 InMail Messaging</h4>
                <p>Send professional messages to LinkedIn members outside your network</p>
                
                <div class="form-group">
                    <label for="inmailRecipientId">Recipient LinkedIn ID:</label>
                    <input type="text" id="inmailRecipientId" placeholder="e.g., ACoAAABCDEF">
                </div>
                
                <div class="form-group">
                    <label for="inmailSubject">Subject:</label>
                    <input type="text" id="inmailSubject" placeholder="Professional Opportunity">
                </div>
                
                <div class="form-group">
                    <label for="inmailMessage">Message:</label>
                    <textarea id="inmailMessage" rows="4" placeholder="Hello, I hope this message finds you well..."></textarea>
                </div>
                
                <button type="button" onclick="sendInMail()">Send InMail</button>
                <div id="inmailResult"></div>
            </div>

            <!-- Company Page Messaging -->
            <div class="messaging-card">
                <h4>🏢 Company Page Messaging</h4>
                <p>Send messages from your company page to prospects and customers</p>
                
                <div class="form-group">
                    <label for="companyId">Company ID:</label>
                    <input type="text" id="companyId" placeholder="e.g., 123456">
                </div>
                
                <div class="form-group">
                    <label for="companyRecipientId">Recipient LinkedIn ID:</label>
                    <input type="text" id="companyRecipientId" placeholder="e.g., ACoAAABCDEF">
                </div>
                
                <div class="form-group">
                    <label for="companyMessage">Company Message:</label>
                    <textarea id="companyMessage" rows="4" placeholder="Thank you for your interest in our company..."></textarea>
                </div>
                
                <button type="button" onclick="sendCompanyMessage()">Send Company Message</button>
                <div id="companyResult"></div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="section">
            <h3>📋 How to Get LinkedIn IDs</h3>
            <div class="info">
                <strong>To find LinkedIn Person/Company IDs:</strong><br>
                1. Visit the LinkedIn profile or company page<br>
                2. Look at the URL: linkedin.com/in/<strong>person-id</strong> or linkedin.com/company/<strong>company-id</strong><br>
                3. Use LinkedIn Sales Navigator or LinkedIn API to get internal IDs<br>
                4. For Unipile integration, accounts will show their IDs in the connection status
            </div>
        </div>
    </div>

    <script>
        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML = 
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }
            
            fetch('/api/linkedin/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Unipile API connected successfully!</div>';
                    checkUnipileStatus();
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function checkUnipileStatus() {
            fetch('/api/linkedin/unipile/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const accounts = data.accounts || [];

                    if (accounts.length > 0) {
                        let html = '<div class="success"><strong>✅ Connected LinkedIn Accounts:</strong><br>';
                        accounts.forEach(account => {
                            html += `💼 Account ID: ${account.id}<br>`;
                            html += `👤 Name: ${account.name || 'N/A'}<br>`;
                            html += `📧 Email: ${account.email || 'N/A'}<br><br>`;
                        });
                        html += '</div>';
                        statusDiv.innerHTML = html;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="info">
                                <strong>📋 Unipile API Connected</strong><br>
                                No LinkedIn accounts found. Use one of the methods above to add a LinkedIn account,
                                then click "Check Connection Status" to refresh.
                            </div>
                        `;
                    }
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Status check error: ' + error.message + '</div>';
            });
        }



        function connectLinkedInDirect() {
            const username = document.getElementById('linkedinUsername').value;
            const password = document.getElementById('linkedinPassword').value;
            const resultDiv = document.getElementById('directConnectResult');

            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter both username and password</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Connecting LinkedIn account...</div>';

            fetch('/api/linkedin/connect-account', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ LinkedIn account connected successfully!<br>
                            Account ID: ${data.account_id || 'N/A'}<br>
                            <small>This account will now appear in your Unipile dashboard.</small>
                        </div>
                    `;
                    // Clear the password field for security
                    document.getElementById('linkedinPassword').value = '';
                    // Refresh status
                    setTimeout(checkUnipileStatus, 2000);
                } else if (data.checkpoint_required) {
                    // Handle checkpoint requirement
                    checkpointAccountId = data.account_id;
                    showCheckpointSection(data.checkpoint_type, data.message);
                    resultDiv.innerHTML = `
                        <div class="info">
                            🔐 Verification Required<br>
                            ${data.message}<br>
                            <small>Please complete the verification below.</small>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + (data.detail || data.error || data.message || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Connection error: ' + error.message + '</div>';
            });
        }

        // Global variable to store checkpoint account ID
        let checkpointAccountId = null;

        function showCheckpointSection(checkpointType, message) {
            const checkpointSection = document.getElementById('checkpointSection');
            const checkpointMessage = document.getElementById('checkpointMessage');
            const checkpointInstructions = document.getElementById('checkpointInstructions');

            checkpointMessage.textContent = message;

            // Update instructions based on checkpoint type
            let instructions = '';
            switch(checkpointType) {
                case '2FA':
                    instructions = 'Enter the 6-digit code from your authenticator app.';
                    break;
                case 'OTP':
                    instructions = 'Check your email or SMS for the verification code.';
                    break;
                case 'IN_APP_VALIDATION':
                    instructions = 'Open your LinkedIn mobile app and approve the connection request.';
                    break;
                case 'PHONE_REGISTER':
                    instructions = 'Enter your phone number with country code, e.g., (+1)5551234567';
                    break;
                case 'CAPTCHA':
                    instructions = 'Complete the CAPTCHA verification.';
                    break;
                default:
                    instructions = 'Follow the verification instructions from LinkedIn.';
            }

            checkpointInstructions.textContent = instructions;
            checkpointSection.style.display = 'block';
        }

        function solveCheckpoint() {
            const code = document.getElementById('verificationCode').value;
            const resultDiv = document.getElementById('checkpointResult');

            if (!code) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter the verification code</div>';
                return;
            }

            if (!checkpointAccountId) {
                resultDiv.innerHTML = '<div class="error">❌ No checkpoint session found. Please try connecting again.</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Verifying code...</div>';

            fetch('/api/linkedin/solve-checkpoint', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    account_id: checkpointAccountId,
                    code: code
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Verification successful!<br>
                            ${data.message}<br>
                            ${data.account_id ? `Account ID: ${data.account_id}` : ''}
                        </div>
                    `;
                    // Hide checkpoint section
                    setTimeout(() => {
                        document.getElementById('checkpointSection').style.display = 'none';
                        document.getElementById('verificationCode').value = '';
                        checkpointAccountId = null;
                    }, 2000);
                    // Refresh status
                    setTimeout(checkUnipileStatus, 3000);
                } else if (data.checkpoint_required) {
                    // Another checkpoint required
                    showCheckpointSection(data.checkpoint_type, data.message);
                    resultDiv.innerHTML = `
                        <div class="info">
                            🔐 Additional verification required<br>
                            ${data.message}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Verification failed: ' + (data.detail || data.error || data.message || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Verification error: ' + error.message + '</div>';
            });
        }

        function sendConnectionRequest() {
            const personId = document.getElementById('connectionPersonId').value;
            const message = document.getElementById('connectionMessage').value;
            
            if (!personId || !message) {
                document.getElementById('connectionResult').innerHTML = 
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }
            
            fetch('/api/linkedin/send-connection-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: personId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('connectionResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection request sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendInMail() {
            const recipientId = document.getElementById('inmailRecipientId').value;
            const subject = document.getElementById('inmailSubject').value;
            const message = document.getElementById('inmailMessage').value;
            
            if (!recipientId || !subject || !message) {
                document.getElementById('inmailResult').innerHTML = 
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }
            
            fetch('/api/linkedin/send-inmail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId,
                    subject: subject,
                    message_body: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('inmailResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ InMail sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('inmailResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendCompanyMessage() {
            const companyId = document.getElementById('companyId').value;
            const recipientId = document.getElementById('companyRecipientId').value;
            const message = document.getElementById('companyMessage').value;
            
            if (!companyId || !recipientId || !message) {
                document.getElementById('companyResult').innerHTML = 
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }
            
            fetch('/api/linkedin/send-company-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: companyId,
                    recipient_id: recipientId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('companyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Company message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('companyResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        // Auto-check status on page load
        window.onload = function() {
            checkUnipileStatus();
        };
    </script>
</body>
</html>
