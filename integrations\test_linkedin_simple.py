#!/usr/bin/env python3
"""
Simple test for LinkedIn integration
"""

import sys
import os
import json
import requests

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_linkedin_api_endpoints():
    """Test LinkedIn API endpoints"""
    print("🌐 Testing LinkedIn API Endpoints")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    try:
        # Test Unipile status endpoint
        print("📊 Testing Unipile status endpoint...")
        response = requests.get(f"{base_url}/api/linkedin/unipile/status")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Unipile status endpoint working")
            print(f"   Success: {data.get('success', False)}")
            print(f"   LinkedIn accounts: {data.get('linkedin_accounts', 0)}")
            
            if data.get('linkedin_accounts', 0) > 0:
                print("🎉 LinkedIn accounts found in Unipile!")
                accounts = data.get('accounts', [])
                for i, account in enumerate(accounts, 1):
                    print(f"   Account {i}: {account.get('name', 'N/A')} ({account.get('id')})")
            else:
                print("⚠️  No LinkedIn accounts found in Unipile dashboard")
                print("💡 To add a LinkedIn account:")
                print("   1. Visit https://dashboard.unipile.com")
                print("   2. Click 'Add Account' → 'LinkedIn'")
                print("   3. Follow the authentication process")
        else:
            print(f"❌ Status endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
        
        # Test connection endpoint
        print("\n🔗 Testing Unipile connection endpoint...")
        response = requests.post(f"{base_url}/api/linkedin/unipile/connect", 
                               json={"api_key": "RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI="})
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Unipile connection endpoint working")
            print(f"   Success: {data.get('success', False)}")
            print(f"   Message: {data.get('message', 'N/A')}")
        else:
            print(f"❌ Connection endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing endpoints: {e}")
        return False

def test_linkedin_auth_page():
    """Test LinkedIn authentication page"""
    print("\n📄 Testing LinkedIn Authentication Page")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:8000/linkedin/linkedin_auth.html")
        
        if response.status_code == 200:
            print("✅ LinkedIn auth page accessible")
            
            # Check for key elements
            content = response.text
            if "Unipile API" in content:
                print("✅ Unipile integration present")
            if "RJkN5s9h.VfDYMEvnEzzP6zARTnmJ5S7v8CCvELn5257wG7PHmBI=" in content:
                print("✅ Default API key configured")
            if "checkUnipileStatus" in content:
                print("✅ Status checking functionality present")
            
            return True
        else:
            print(f"❌ Auth page failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing auth page: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Simple LinkedIn Integration Test")
    print("=" * 60)
    
    # Test endpoints
    endpoints_ok = test_linkedin_api_endpoints()
    
    # Test auth page
    auth_page_ok = test_linkedin_auth_page()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   API Endpoints: {'✅ PASSED' if endpoints_ok else '❌ FAILED'}")
    print(f"   Auth Page: {'✅ PASSED' if auth_page_ok else '❌ FAILED'}")
    
    if endpoints_ok and auth_page_ok:
        print("\n🎉 LinkedIn integration is working!")
        print("\n💡 Next steps:")
        print("   1. Open http://localhost:8000/linkedin/linkedin_auth.html")
        print("   2. Click 'Connect via Unipile'")
        print("   3. Add LinkedIn accounts to your Unipile dashboard")
        print("   4. Test messaging functionality")
    else:
        print("\n⚠️  Some tests failed. Please check the server logs.")
    
    print("\n🔗 LinkedIn Auth Page: http://localhost:8000/linkedin/linkedin_auth.html")
    print("🔗 Unipile Dashboard: https://dashboard.unipile.com")
