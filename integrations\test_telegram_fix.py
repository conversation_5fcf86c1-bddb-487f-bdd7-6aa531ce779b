#!/usr/bin/env python3
"""
Test script to verify Telegram API fixes
"""

import sys
import os
import json
import traceback

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_telegram_api():
    """Test the Telegram API functionality"""
    print("🤖 Testing Telegram API Integration")
    print("=" * 50)
    
    try:
        # Import the TelegramMessaging class
        from telegram_integration.telegram_api import TelegramMessaging
        print("✅ Successfully imported TelegramMessaging")
        
        # Create an instance
        telegram = TelegramMessaging()
        print("✅ Successfully created TelegramMessaging instance")
        
        # Check configuration
        print(f"📋 Bot token configured: {bool(telegram.bot_token)}")
        print(f"📋 Connection status: {telegram.connection_status}")
        
        # Test connection
        print("\n🔗 Testing bot connection...")
        connection_result = telegram.test_connection()
        print(f"Connection result: {json.dumps(connection_result, indent=2)}")
        
        if connection_result.get("success"):
            print("✅ Bot connection successful!")
            
            # Test message sending with a dummy chat ID
            print("\n📤 Testing message sending (with dummy chat ID)...")
            test_message_result = telegram.send_message("123456789", "Test message from fixed API")
            print(f"Message result: {json.dumps(test_message_result, indent=2)}")
            
            # The message will likely fail because 123456789 is not a valid chat ID,
            # but we should see proper error handling now
            if test_message_result.get("error"):
                print("⚠️  Message failed as expected (dummy chat ID), but error handling is working!")
            else:
                print("✅ Message sent successfully!")
                
        else:
            print("❌ Bot connection failed")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_api_endpoint():
    """Test the API endpoint functionality"""
    print("\n🌐 Testing API Endpoint")
    print("=" * 30)
    
    try:
        # Test importing the API endpoints
        from api.api_endpoints import app
        print("✅ Successfully imported FastAPI app")
        
        # Test the TelegramTestMessageRequest model
        from api.api_endpoints import TelegramTestMessageRequest
        print("✅ Successfully imported TelegramTestMessageRequest")
        
        # Create a test request
        test_request = TelegramTestMessageRequest(
            chat_id="123456789",
            message="Test message"
        )
        print(f"✅ Created test request: {test_request}")
        
    except Exception as e:
        print(f"❌ Error testing API endpoint: {e}")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Telegram API Fix Verification")
    print("=" * 60)
    
    # Test the core API
    api_success = test_telegram_api()
    
    # Test the endpoint
    endpoint_success = test_api_endpoint()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Core API Test: {'✅ PASSED' if api_success else '❌ FAILED'}")
    print(f"   Endpoint Test: {'✅ PASSED' if endpoint_success else '❌ FAILED'}")
    
    if api_success and endpoint_success:
        print("\n🎉 All tests passed! The Telegram API fix should be working.")
        print("\n💡 Next steps:")
        print("   1. Start the server: python api/api_endpoints.py")
        print("   2. Test with a real chat ID using the /api/telegram/send-test endpoint")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
