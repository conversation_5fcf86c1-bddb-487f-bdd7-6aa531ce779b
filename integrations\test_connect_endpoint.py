#!/usr/bin/env python3
"""
Test script for the LinkedIn connect-account endpoint
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_endpoint_directly():
    """Test the endpoint function directly without HTTP"""
    print("🔧 Testing LinkedIn connect-account endpoint directly")
    print("=" * 50)
    
    try:
        # Import the endpoint function
        from api.api_endpoints import connect_linkedin_account_direct, LinkedInDirectConnectRequest
        
        print("✅ Successfully imported endpoint function")
        
        # Create a test request
        class MockRequest:
            def __init__(self, username, password):
                self.username = username
                self.password = password
        
        test_request = MockRequest("<EMAIL>", "testpass")
        
        print("📝 Created test request")
        print(f"   Username: {test_request.username}")
        print(f"   Password: {'*' * len(test_request.password)}")
        
        # Test the function (this will likely fail due to invalid credentials, but we can see the flow)
        print("\n🚀 Testing endpoint function...")
        
        import asyncio
        
        async def run_test():
            try:
                result = await connect_linkedin_account_direct(test_request)
                print(f"✅ Endpoint returned: {result}")
                return True
            except Exception as e:
                print(f"⚠️  Expected error (invalid credentials): {e}")
                # Check if it's the expected error type
                if "Unipile client not available" in str(e) or "Failed to connect LinkedIn account" in str(e):
                    print("✅ Endpoint is working correctly (expected error for test credentials)")
                    return True
                else:
                    print(f"❌ Unexpected error: {e}")
                    return False
        
        result = asyncio.run(run_test())
        return result
        
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_http_endpoint():
    """Test the endpoint via HTTP"""
    print("\n🌐 Testing LinkedIn connect-account endpoint via HTTP")
    print("=" * 50)
    
    try:
        import requests
        import time
        
        # Try to connect to the server
        print("🔍 Checking if server is running...")
        
        for attempt in range(3):
            try:
                response = requests.get("http://localhost:8000/api/linkedin/status", timeout=2)
                if response.status_code == 200:
                    print("✅ Server is running!")
                    break
                else:
                    print(f"⚠️  Server responded with status {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"❌ Attempt {attempt + 1}: Server not responding")
                if attempt < 2:
                    print("   Waiting 2 seconds before retry...")
                    time.sleep(2)
                else:
                    print("❌ Server is not running. Please start the server first.")
                    return False
            except Exception as e:
                print(f"❌ Connection error: {e}")
                return False
        
        # Test the connect-account endpoint
        print("\n📤 Testing connect-account endpoint...")
        
        test_data = {
            "username": "<EMAIL>",
            "password": "testpass"
        }
        
        try:
            response = requests.post(
                "http://localhost:8000/api/linkedin/connect-account",
                json=test_data,
                timeout=10
            )
            
            print(f"📊 Response Status: {response.status_code}")
            print(f"📄 Response Body: {response.text}")
            
            if response.status_code == 404:
                print("❌ Endpoint not found (404) - This indicates the endpoint is not properly registered")
                return False
            elif response.status_code in [400, 500]:
                print("✅ Endpoint found but returned expected error (invalid test credentials)")
                return True
            else:
                print(f"✅ Endpoint responded with status {response.status_code}")
                return True
                
        except Exception as e:
            print(f"❌ Request error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ HTTP test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 LinkedIn Connect-Account Endpoint Test Suite")
    print("=" * 60)
    
    # Test 1: Direct function test
    direct_result = test_endpoint_directly()
    
    # Test 2: HTTP endpoint test
    http_result = test_http_endpoint()
    
    print("\n📊 Test Results Summary")
    print("=" * 30)
    print(f"Direct Function Test: {'✅ PASS' if direct_result else '❌ FAIL'}")
    print(f"HTTP Endpoint Test:   {'✅ PASS' if http_result else '❌ FAIL'}")
    
    if direct_result and not http_result:
        print("\n💡 Diagnosis: Endpoint function works but HTTP server issue")
        print("   Solution: Restart the server or check server configuration")
    elif not direct_result:
        print("\n💡 Diagnosis: Endpoint function has issues")
        print("   Solution: Check endpoint implementation")
    elif direct_result and http_result:
        print("\n🎉 All tests passed! Endpoint is working correctly.")
    
    return direct_result and http_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
