#!/usr/bin/env python3
"""
Script to get chat IDs from recent messages to the Telegram bot
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def get_recent_chats():
    """Get recent chat IDs from bot updates"""
    print("🔍 Getting recent chat IDs from bot updates...")
    print("=" * 50)
    
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        
        # Create bot instance
        telegram = TelegramMessaging()
        
        # Check if bot is connected
        if telegram.connection_status != "connected":
            print("❌ Bot is not connected. Please check the bot token.")
            return
        
        print(f"✅ Bot connected: @{telegram.bot_info.get('username', 'Unknown')}")
        
        # Get recent updates
        print("\n📥 Fetching recent updates...")
        updates = telegram.get_updates()
        
        if updates.get("ok"):
            messages = updates.get("result", [])
            print(f"Found {len(messages)} recent updates")
            
            if not messages:
                print("\n⚠️  No recent messages found!")
                print("💡 To get your chat ID:")
                print("   1. Open Telegram")
                print("   2. Search for @Amiee2_bot")
                print("   3. Send any message to the bot (like 'hello')")
                print("   4. Run this script again")
                return
            
            # Extract unique chat IDs
            chat_ids = set()
            chat_info = {}
            
            for update in messages:
                if "message" in update:
                    message = update["message"]
                    chat = message.get("chat", {})
                    chat_id = chat.get("id")
                    
                    if chat_id:
                        chat_ids.add(chat_id)
                        chat_info[chat_id] = {
                            "type": chat.get("type"),
                            "title": chat.get("title"),
                            "username": chat.get("username"),
                            "first_name": chat.get("first_name"),
                            "last_name": chat.get("last_name")
                        }
            
            print(f"\n📋 Found {len(chat_ids)} unique chat(s):")
            print("=" * 40)
            
            for chat_id in chat_ids:
                info = chat_info[chat_id]
                print(f"\n💬 Chat ID: {chat_id}")
                print(f"   Type: {info['type']}")
                
                if info['type'] == 'private':
                    name_parts = []
                    if info['first_name']:
                        name_parts.append(info['first_name'])
                    if info['last_name']:
                        name_parts.append(info['last_name'])
                    name = " ".join(name_parts) if name_parts else "Unknown"
                    print(f"   Name: {name}")
                    if info['username']:
                        print(f"   Username: @{info['username']}")
                elif info['type'] in ['group', 'supergroup']:
                    print(f"   Title: {info['title']}")
                    if info['username']:
                        print(f"   Username: @{info['username']}")
                
                print(f"   ✅ Use this chat ID to test: {chat_id}")
            
            print("\n" + "=" * 50)
            print("💡 Copy one of the chat IDs above to test your bot!")
            
        else:
            print(f"❌ Failed to get updates: {updates.get('description', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_message_to_chat(chat_id, message="Hello! This is a test message from your Telegram bot."):
    """Test sending a message to a specific chat ID"""
    print(f"\n📤 Testing message to chat ID: {chat_id}")
    print("=" * 40)
    
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        
        telegram = TelegramMessaging()
        
        if telegram.connection_status != "connected":
            print("❌ Bot is not connected.")
            return False
        
        result = telegram.send_message(chat_id, message)
        
        if result.get("success") or result.get("ok"):
            print("✅ Message sent successfully!")
            print(f"   Message ID: {result.get('message_id')}")
            return True
        else:
            print(f"❌ Failed to send message: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🤖 Telegram Chat ID Helper")
    print("=" * 60)
    
    # Get recent chat IDs
    get_recent_chats()
    
    # Ask user if they want to test with a specific chat ID
    print("\n" + "=" * 60)
    test_chat = input("Enter a chat ID to test (or press Enter to skip): ").strip()
    
    if test_chat:
        try:
            chat_id = int(test_chat)
            test_message_to_chat(chat_id)
        except ValueError:
            print("❌ Invalid chat ID. Please enter a numeric chat ID.")
